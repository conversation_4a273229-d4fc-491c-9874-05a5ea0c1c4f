import pandas as pd
import torch
import numpy as np
import os
from tqdm import tqdm

# Import existing components
from ensemble_processor import create_dual_dataloaders
from process import VietnameseTextPreprocessor, preprocess_and_tokenize
from EvaluationSystemByFile import evaluation_system_by_file

# Import new advanced components
from ensemble_model import AdvancedPhoBERTModel, AdvancedInfoXLMModel, AttentionBasedEnsemble
from advanced_training import AdvancedTrainer
from model_visualization import save_all_visualizations, plot_training_history

# Configuration
class Config:
    # Data paths
    TRAIN_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Train.csv'
    VAL_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Dev.csv'
    TEST_PATH = '/kaggle/working/absa-dataset/ABSA_Dataset/Res_ABSA/Test.csv'
    
    # Model hyperparameters
    MAX_LENGTH = 256
    BATCH_SIZE = 16  # Smaller due to larger models
    HIDDEN_SIZE = 768
    DROPOUT = 0.3
    NUM_ATTENTION_HEADS = 8
    
    # Training hyperparameters
    LEARNING_RATE = 2e-5
    WEIGHT_DECAY = 0.01
    NUM_EPOCHS = 5
    GRADIENT_ACCUMULATION_STEPS = 2
    MAX_GRAD_NORM = 1.0
    
    # Loss function parameters
    FOCAL_ALPHA = 1.0
    FOCAL_GAMMA = 2.0
    LABEL_SMOOTHING = 0.1
    
    # Advanced model settings
    USE_LAYER_NORM = True
    USE_RESIDUAL = True
    FUSION_DROPOUT = 0.2
    
    # Scheduler
    SCHEDULER_TYPE = 'cosine'  # 'cosine' or 'plateau'
    
    # Output paths
    MODEL_SAVE_PATH = 'best_advanced_ensemble_model.pth'
    PREDICTIONS_DIR = 'predictions'
    VISUALIZATIONS_DIR = 'visualizations'


def setup_data_and_preprocessing():
    """Setup data loading and preprocessing"""
    print("Setting up data and preprocessing...")
    
    # Initialize preprocessor
    vn_preprocessor = VietnameseTextPreprocessor(
        extra_teencodes={
            'khách sạn': ['ks'], 'nhà hàng': ['nhahang'], 'nhân viên': ['nv'],
        }, 
        max_correction_length=Config.MAX_LENGTH
    )
    
    # Load data
    df_train = pd.read_csv(Config.TRAIN_PATH, encoding='utf8')
    df_val = pd.read_csv(Config.VAL_PATH, encoding='utf8')
    df_test = pd.read_csv(Config.TEST_PATH, encoding='utf8')
    
    print(f"Training samples: {len(df_train)}")
    print(f"Validation samples: {len(df_val)}")
    print(f"Test samples: {len(df_test)}")
    
    # Preprocess text
    df_train = preprocess_and_tokenize(df_train, "Review", vn_preprocessor, None, 
                                     Config.BATCH_SIZE, Config.MAX_LENGTH)
    df_val = preprocess_and_tokenize(df_val, "Review", vn_preprocessor, None, 
                                   Config.BATCH_SIZE, Config.MAX_LENGTH)
    df_test = preprocess_and_tokenize(df_test, "Review", vn_preprocessor, None, 
                                    Config.BATCH_SIZE, Config.MAX_LENGTH)
    
    # Create dataloaders
    train_loader, val_loader, test_loader = create_dual_dataloaders(
        df_train, df_val, df_test,
        batch_size=Config.BATCH_SIZE,
        max_length=Config.MAX_LENGTH
    )
    
    return train_loader, val_loader, test_loader, df_test


def create_advanced_models(num_aspects, device):
    """Create advanced models with InfoXLM"""
    print("Creating advanced models...")
    
    # Advanced PhoBERT model
    phobert_model = AdvancedPhoBERTModel(
        pretrained_model='vinai/phobert-base',
        num_aspects=num_aspects,
        hidden_size=Config.HIDDEN_SIZE,
        dropout=Config.DROPOUT,
        num_attention_heads=Config.NUM_ATTENTION_HEADS,
        use_layer_norm=Config.USE_LAYER_NORM,
        use_residual=Config.USE_RESIDUAL
    ).to(device)
    
    # Advanced InfoXLM model
    infoxlm_model = AdvancedInfoXLMModel(
        pretrained_model='microsoft/infoxlm-base',
        num_aspects=num_aspects,
        hidden_size=Config.HIDDEN_SIZE,
        dropout=Config.DROPOUT,
        num_attention_heads=Config.NUM_ATTENTION_HEADS,
        use_layer_norm=Config.USE_LAYER_NORM,
        use_residual=Config.USE_RESIDUAL
    ).to(device)
    
    # Attention-based ensemble
    ensemble_model = AttentionBasedEnsemble(
        num_aspects=num_aspects,
        models=[phobert_model, infoxlm_model],
        hidden_size=Config.HIDDEN_SIZE,
        fusion_dropout=Config.FUSION_DROPOUT
    ).to(device)
    
    print(f"PhoBERT parameters: {sum(p.numel() for p in phobert_model.parameters()):,}")
    print(f"InfoXLM parameters: {sum(p.numel() for p in infoxlm_model.parameters()):,}")
    print(f"Ensemble parameters: {sum(p.numel() for p in ensemble_model.parameters()):,}")
    
    return ensemble_model


def train_advanced_model(model, train_loader, val_loader, device, num_aspects):
    """Train the advanced ensemble model"""
    print("Setting up advanced training...")
    
    # Create advanced trainer
    trainer = AdvancedTrainer(
        model=model,
        device=device,
        num_aspects=num_aspects,
        focal_alpha=Config.FOCAL_ALPHA,
        focal_gamma=Config.FOCAL_GAMMA,
        label_smoothing=Config.LABEL_SMOOTHING,
        gradient_accumulation_steps=Config.GRADIENT_ACCUMULATION_STEPS,
        max_grad_norm=Config.MAX_GRAD_NORM
    )
    
    # Setup optimizer and scheduler
    trainer.setup_optimizer_and_scheduler(
        learning_rate=Config.LEARNING_RATE,
        weight_decay=Config.WEIGHT_DECAY,
        scheduler_type=Config.SCHEDULER_TYPE,
        num_epochs=Config.NUM_EPOCHS
    )
    
    # Train the model
    print("Starting advanced training...")
    best_model_state, best_val_loss = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=Config.NUM_EPOCHS,
        save_path=Config.MODEL_SAVE_PATH
    )
    
    # Plot training history
    history = trainer.get_training_history()
    if history['train_losses']:
        fig = plot_training_history(history)
        os.makedirs(Config.VISUALIZATIONS_DIR, exist_ok=True)
        fig.savefig(f'{Config.VISUALIZATIONS_DIR}/training_history.png', 
                   dpi=300, bbox_inches='tight')
        print(f"Training history saved to {Config.VISUALIZATIONS_DIR}/training_history.png")
    
    return best_model_state, best_val_loss


def generate_predictions(model, test_loader, device, aspect_columns):
    """Generate predictions using the trained model"""
    print("Generating predictions...")
    
    model.eval()
    all_predictions = []
    all_texts = []
    
    # Mapping from index to sentiment label
    idx_to_sentiment = {0: 'None', 1: 'positive', 2: 'negative', 3: 'neutral'}
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Generating predictions"):
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # Get review texts
            texts = batch.get('review_text', [])
            all_texts.extend(texts)
            
            # Forward pass
            outputs = model(batch)
            
            # Get predictions
            predictions = torch.argmax(outputs, dim=-1).cpu().numpy()
            all_predictions.append(predictions)
    
    # Combine all predictions
    all_predictions = np.vstack(all_predictions)
    
    return all_predictions, all_texts


def save_prediction_files(predictions, texts, aspect_columns, test_loader):
    """Save prediction and gold files for evaluation"""
    os.makedirs(Config.PREDICTIONS_DIR, exist_ok=True)
    
    test_output_path = f"{Config.PREDICTIONS_DIR}/test_predictions.txt"
    gold_output_path = f"{Config.PREDICTIONS_DIR}/gold_labels.txt"
    
    idx_to_sentiment = {0: 'None', 1: 'positive', 2: 'negative', 3: 'neutral'}
    
    # Save predictions
    with open(test_output_path, 'w', encoding='utf-8') as f:
        for i in range(len(texts)):
            f.write(f"#{i+1}\n")
            f.write(f"{texts[i]}\n")
            
            # Create aspect-sentiment pairs
            aspects = []
            for j, aspect in enumerate(aspect_columns):
                sentiment_idx = predictions[i][j]
                if sentiment_idx != 0:  # Skip 'None' sentiment
                    aspects.append(f"{{{aspect}, {idx_to_sentiment[sentiment_idx]}}}")
            
            # Write aspects line
            if aspects:
                f.write(", ".join(aspects) + "\n\n")
            else:
                f.write("\n\n")
    
    # Save gold labels
    all_labels = []
    for batch in test_loader:
        labels = batch['labels'].cpu().numpy()
        all_labels.append(labels)
    all_labels = np.vstack(all_labels)
    
    with open(gold_output_path, 'w', encoding='utf-8') as f:
        for i in range(len(texts)):
            f.write(f"#{i+1}\n")
            f.write(f"{texts[i]}\n")
            
            # Create aspect-sentiment pairs
            aspects = []
            for j, aspect in enumerate(aspect_columns):
                sentiment_idx = np.argmax(all_labels[i][j])
                if sentiment_idx != 0:  # Skip 'None' sentiment
                    aspects.append(f"{{{aspect}, {idx_to_sentiment[sentiment_idx]}}}")
            
            # Write aspects line
            if aspects:
                f.write(", ".join(aspects) + "\n\n")
            else:
                f.write("\n\n")
    
    return test_output_path, gold_output_path


def main():
    """Main execution function"""
    print("=" * 60)
    print("Advanced ABSA Ensemble with InfoXLM")
    print("=" * 60)
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Setup data
    train_loader, val_loader, test_loader, df_test = setup_data_and_preprocessing()
    
    # Get number of aspects
    aspect_columns = [col for col in df_test.columns if col != 'Review']
    num_aspects = len(aspect_columns)
    print(f"Number of aspects: {num_aspects}")
    print(f"Aspects: {aspect_columns}")
    
    # Create models
    ensemble_model = create_advanced_models(num_aspects, device)
    
    # Train model
    best_model_state, best_val_loss = train_advanced_model(
        ensemble_model, train_loader, val_loader, device, num_aspects
    )
    
    # Load best model for evaluation
    ensemble_model.load_state_dict(best_model_state)
    
    # Generate predictions
    predictions, texts = generate_predictions(ensemble_model, test_loader, device, aspect_columns)
    
    # Save prediction files
    test_output_path, gold_output_path = save_prediction_files(
        predictions, texts, aspect_columns, test_loader
    )
    
    # Evaluate using existing system
    print("\n" + "=" * 60)
    print("EVALUATION RESULTS")
    print("=" * 60)
    evaluation_system_by_file(gold_output_path, test_output_path)
    
    # Generate visualizations
    print("\nGenerating visualizations...")
    save_all_visualizations()
    
    print(f"\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")
    print(f"Model saved to: {Config.MODEL_SAVE_PATH}")
    print(f"Predictions saved to: {Config.PREDICTIONS_DIR}")
    print(f"Visualizations saved to: {Config.VISUALIZATIONS_DIR}")


if __name__ == "__main__":
    main()
