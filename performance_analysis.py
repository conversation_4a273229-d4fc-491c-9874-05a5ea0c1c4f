import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import precision_recall_fscore_support, accuracy_score
import torch
from datetime import datetime
import json


class PerformanceAnalyzer:
    """Comprehensive performance analysis for ABSA models"""
    
    def __init__(self):
        self.results = {}
        self.baseline_results = {
            'precision': 73.54,
            'recall': 73.91,
            'f1': 73.72
        }
        
    def analyze_predictions(self, predictions, labels, aspect_columns, model_name="Advanced Model"):
        """Analyze model predictions and compute metrics"""
        
        # Convert to numpy if tensor
        if torch.is_tensor(predictions):
            predictions = predictions.cpu().numpy()
        if torch.is_tensor(labels):
            labels = labels.cpu().numpy()
            
        # Compute overall metrics
        overall_precision, overall_recall, overall_f1, _ = precision_recall_fscore_support(
            labels.flatten(), predictions.flatten(), average='weighted', zero_division=0
        )
        
        overall_accuracy = accuracy_score(labels.flatten(), predictions.flatten())
        
        # Compute per-aspect metrics
        aspect_metrics = {}
        for i, aspect in enumerate(aspect_columns):
            precision, recall, f1, _ = precision_recall_fscore_support(
                labels[:, i], predictions[:, i], average='weighted', zero_division=0
            )
            accuracy = accuracy_score(labels[:, i], predictions[:, i])
            
            aspect_metrics[aspect] = {
                'precision': precision * 100,
                'recall': recall * 100,
                'f1': f1 * 100,
                'accuracy': accuracy * 100
            }
        
        # Store results
        self.results[model_name] = {
            'overall': {
                'precision': overall_precision * 100,
                'recall': overall_recall * 100,
                'f1': overall_f1 * 100,
                'accuracy': overall_accuracy * 100
            },
            'aspects': aspect_metrics,
            'timestamp': datetime.now().isoformat()
        }
        
        return self.results[model_name]
    
    def compare_with_baseline(self, model_name="Advanced Model"):
        """Compare current model with baseline"""
        if model_name not in self.results:
            print(f"No results found for {model_name}")
            return
            
        current = self.results[model_name]['overall']
        baseline = self.baseline_results
        
        print(f"\n{'='*60}")
        print(f"PERFORMANCE COMPARISON: {model_name} vs Baseline")
        print(f"{'='*60}")
        
        metrics = ['precision', 'recall', 'f1']
        improvements = {}
        
        for metric in metrics:
            current_val = current[metric]
            baseline_val = baseline[metric]
            improvement = current_val - baseline_val
            improvement_pct = (improvement / baseline_val) * 100
            
            improvements[metric] = improvement
            
            print(f"{metric.capitalize():>10}: {baseline_val:6.2f}% → {current_val:6.2f}% "
                  f"({improvement:+6.2f}%, {improvement_pct:+5.1f}%)")
        
        # Overall assessment
        avg_improvement = np.mean(list(improvements.values()))
        print(f"\nAverage Improvement: {avg_improvement:+6.2f}%")
        
        if avg_improvement > 10:
            print("🎉 EXCELLENT improvement! Target exceeded!")
        elif avg_improvement > 5:
            print("✅ GOOD improvement! On track to meet target!")
        elif avg_improvement > 0:
            print("📈 POSITIVE improvement! Consider further tuning.")
        else:
            print("⚠️  Need more improvement. Check hyperparameters.")
            
        return improvements
    
    def plot_performance_comparison(self, save_path="performance_comparison.png"):
        """Plot performance comparison"""
        if not self.results:
            print("No results to plot")
            return
            
        # Prepare data
        models = ['Baseline'] + list(self.results.keys())
        metrics = ['precision', 'recall', 'f1']
        
        data = []
        for metric in metrics:
            baseline_val = self.baseline_results[metric]
            data.append([baseline_val])
            
            for model_name in self.results.keys():
                current_val = self.results[model_name]['overall'][metric]
                data[-1].append(current_val)
        
        # Create plot
        x = np.arange(len(models))
        width = 0.25
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        
        for i, (metric, values) in enumerate(zip(metrics, data)):
            bars = ax.bar(x + i * width, values, width, label=metric.capitalize(), 
                         color=colors[i], alpha=0.8)
            
            # Add value labels
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        ax.set_xlabel('Models', fontsize=12, fontweight='bold')
        ax.set_ylabel('Performance (%)', fontsize=12, fontweight='bold')
        ax.set_title('Performance Comparison: Baseline vs Advanced Models', 
                    fontsize=14, fontweight='bold')
        ax.set_xticks(x + width)
        ax.set_xticklabels(models)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 100)
        
        # Add target line
        ax.axhline(y=85, color='red', linestyle='--', alpha=0.7, label='Target (85%)')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Performance comparison saved to {save_path}")
    
    def plot_aspect_performance(self, model_name="Advanced Model", save_path="aspect_performance.png"):
        """Plot per-aspect performance"""
        if model_name not in self.results:
            print(f"No results found for {model_name}")
            return
            
        aspects = list(self.results[model_name]['aspects'].keys())
        metrics = ['precision', 'recall', 'f1']
        
        # Prepare data
        data = {metric: [] for metric in metrics}
        for aspect in aspects:
            for metric in metrics:
                data[metric].append(self.results[model_name]['aspects'][aspect][metric])
        
        # Create plot
        x = np.arange(len(aspects))
        width = 0.25
        
        fig, ax = plt.subplots(figsize=(14, 8))
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        
        for i, (metric, values) in enumerate(data.items()):
            bars = ax.bar(x + i * width, values, width, label=metric.capitalize(), 
                         color=colors[i], alpha=0.8)
            
            # Add value labels
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{height:.1f}', ha='center', va='bottom', fontsize=9)
        
        ax.set_xlabel('Aspects', fontsize=12, fontweight='bold')
        ax.set_ylabel('Performance (%)', fontsize=12, fontweight='bold')
        ax.set_title(f'Per-Aspect Performance: {model_name}', 
                    fontsize=14, fontweight='bold')
        ax.set_xticks(x + width)
        ax.set_xticklabels(aspects, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 100)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Aspect performance saved to {save_path}")
    
    def generate_report(self, model_name="Advanced Model", save_path="performance_report.txt"):
        """Generate comprehensive performance report"""
        if model_name not in self.results:
            print(f"No results found for {model_name}")
            return
            
        report_lines = []
        report_lines.append("="*80)
        report_lines.append(f"PERFORMANCE ANALYSIS REPORT - {model_name}")
        report_lines.append("="*80)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Overall performance
        overall = self.results[model_name]['overall']
        report_lines.append("OVERALL PERFORMANCE:")
        report_lines.append("-" * 20)
        report_lines.append(f"Precision: {overall['precision']:.2f}%")
        report_lines.append(f"Recall:    {overall['recall']:.2f}%")
        report_lines.append(f"F1-Score:  {overall['f1']:.2f}%")
        report_lines.append(f"Accuracy:  {overall['accuracy']:.2f}%")
        report_lines.append("")
        
        # Comparison with baseline
        report_lines.append("COMPARISON WITH BASELINE:")
        report_lines.append("-" * 25)
        baseline = self.baseline_results
        for metric in ['precision', 'recall', 'f1']:
            current_val = overall[metric]
            baseline_val = baseline[metric]
            improvement = current_val - baseline_val
            report_lines.append(f"{metric.capitalize()}: {baseline_val:.2f}% → {current_val:.2f}% "
                              f"({improvement:+.2f}%)")
        report_lines.append("")
        
        # Per-aspect performance
        report_lines.append("PER-ASPECT PERFORMANCE:")
        report_lines.append("-" * 23)
        aspects = self.results[model_name]['aspects']
        
        # Header
        report_lines.append(f"{'Aspect':<25} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Accuracy':<10}")
        report_lines.append("-" * 75)
        
        for aspect, metrics in aspects.items():
            report_lines.append(f"{aspect:<25} {metrics['precision']:<10.2f} "
                              f"{metrics['recall']:<10.2f} {metrics['f1']:<10.2f} "
                              f"{metrics['accuracy']:<10.2f}")
        
        report_lines.append("")
        
        # Recommendations
        avg_f1 = overall['f1']
        report_lines.append("RECOMMENDATIONS:")
        report_lines.append("-" * 15)
        
        if avg_f1 >= 85:
            report_lines.append("✅ Excellent performance! Target achieved.")
            report_lines.append("   Consider deploying this model.")
        elif avg_f1 >= 80:
            report_lines.append("📈 Good performance! Close to target.")
            report_lines.append("   Fine-tune hyperparameters for final improvement.")
        elif avg_f1 >= 75:
            report_lines.append("⚠️  Moderate improvement. Consider:")
            report_lines.append("   - Adjusting learning rate")
            report_lines.append("   - Increasing model complexity")
            report_lines.append("   - Data augmentation")
        else:
            report_lines.append("❌ Needs significant improvement. Consider:")
            report_lines.append("   - Reviewing model architecture")
            report_lines.append("   - Checking data quality")
            report_lines.append("   - Hyperparameter optimization")
        
        # Save report
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        # Print report
        for line in report_lines:
            print(line)
        
        print(f"\nReport saved to {save_path}")
    
    def save_results(self, save_path="results.json"):
        """Save results to JSON file"""
        with open(save_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"Results saved to {save_path}")
    
    def load_results(self, load_path="results.json"):
        """Load results from JSON file"""
        try:
            with open(load_path, 'r') as f:
                self.results = json.load(f)
            print(f"Results loaded from {load_path}")
        except FileNotFoundError:
            print(f"File {load_path} not found")


def main():
    """Example usage"""
    analyzer = PerformanceAnalyzer()
    
    # Example: analyze dummy predictions
    # In real usage, you would pass actual predictions and labels
    dummy_predictions = np.random.randint(0, 4, (100, 4))
    dummy_labels = np.random.randint(0, 4, (100, 4))
    aspect_columns = ['RESTAURANT#GENERAL', 'FOOD#QUALITY', 'ROOM#GENERAL', 'SERVICE#GENERAL']
    
    # Simulate improved performance
    dummy_predictions = dummy_labels.copy()  # Perfect predictions for demo
    noise = np.random.randint(0, 4, (20, 4))  # Add some noise
    dummy_predictions[:20] = noise
    
    # Analyze
    results = analyzer.analyze_predictions(dummy_predictions, dummy_labels, aspect_columns)
    
    # Compare and generate reports
    analyzer.compare_with_baseline()
    analyzer.plot_performance_comparison()
    analyzer.plot_aspect_performance()
    analyzer.generate_report()
    analyzer.save_results()


if __name__ == "__main__":
    main()
