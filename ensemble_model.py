import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer
from torch.utils.data import DataLoader
import math

class PhoBERTModel(torch.nn.Module):
    def __init__(self, pretrained_model='vinai/phobert-base', num_aspects=4, hidden_size=768, dropout=0.3):
        super(PhoBERTModel, self).__init__()
        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.classifier = torch.nn.ModuleList([
            torch.nn.Sequential(
                torch.nn.Linear(hidden_size, hidden_size),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size, 4)  # 4 classes: None, Positive, Negative, Neutral
            ) for _ in range(num_aspects)
        ])

    def forward(self, input_ids, attention_mask, token_type_ids):
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        pooled_output = outputs.last_hidden_state[:, 0]  # [CLS] token

        sentiment_logits = []
        for aspect_classifier in self.classifier:
            sentiment_logits.append(aspect_classifier(pooled_output))

        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]


class AdvancedPhoBERTModel(nn.Module):
    """Advanced PhoBERT model with multi-head attention, layer normalization, and advanced pooling"""

    def __init__(self, pretrained_model='vinai/phobert-base', num_aspects=4, hidden_size=768,
                 dropout=0.3, num_attention_heads=8, use_layer_norm=True, use_residual=True):
        super(AdvancedPhoBERTModel, self).__init__()

        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.hidden_size = hidden_size
        self.num_aspects = num_aspects
        self.use_layer_norm = use_layer_norm
        self.use_residual = use_residual

        # Multi-head self-attention for enhanced representation
        self.self_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_attention_heads,
            dropout=dropout,
            batch_first=True
        )

        # Layer normalization
        if self.use_layer_norm:
            self.layer_norm1 = nn.LayerNorm(hidden_size)
            self.layer_norm2 = nn.LayerNorm(hidden_size)

        # Advanced pooling strategies
        self.pooling_strategy = 'multi'  # 'cls', 'mean', 'max', 'multi'

        if self.pooling_strategy == 'multi':
            # Combine multiple pooling strategies
            pooled_size = hidden_size * 3  # CLS + Mean + Max
        else:
            pooled_size = hidden_size

        # Aspect-specific attention and classification heads
        self.aspect_attention = nn.ModuleList([
            nn.Sequential(
                nn.Linear(pooled_size, hidden_size),
                nn.Tanh(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size, 1)
            ) for _ in range(num_aspects)
        ])

        self.aspect_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(pooled_size, hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size, hidden_size // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size // 2, 4)  # 4 sentiment classes
            ) for _ in range(num_aspects)
        ])

        # Dropout layer
        self.dropout = nn.Dropout(dropout)

    def advanced_pooling(self, hidden_states, attention_mask):
        """Advanced pooling combining multiple strategies"""
        # CLS token
        cls_output = hidden_states[:, 0]  # [batch_size, hidden_size]

        if self.pooling_strategy == 'cls':
            return cls_output
        elif self.pooling_strategy == 'mean':
            # Mean pooling with attention mask
            mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
            sum_embeddings = torch.sum(hidden_states * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            return sum_embeddings / sum_mask
        elif self.pooling_strategy == 'max':
            # Max pooling
            return torch.max(hidden_states, dim=1)[0]
        elif self.pooling_strategy == 'multi':
            # Combine CLS, mean, and max pooling
            mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()

            # Mean pooling
            sum_embeddings = torch.sum(hidden_states * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            mean_output = sum_embeddings / sum_mask

            # Max pooling
            max_output = torch.max(hidden_states, dim=1)[0]

            # Concatenate all pooling strategies
            return torch.cat([cls_output, mean_output, max_output], dim=-1)

    def forward(self, input_ids, attention_mask, token_type_ids):
        # Get base encoder outputs
        encoder_outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        hidden_states = encoder_outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]

        # Apply self-attention for enhanced representation
        attended_output, _ = self.self_attention(hidden_states, hidden_states, hidden_states,
                                                key_padding_mask=~attention_mask.bool())

        # Apply layer normalization and residual connection
        if self.use_layer_norm:
            attended_output = self.layer_norm1(attended_output)

        if self.use_residual:
            attended_output = attended_output + hidden_states

        # Apply dropout
        attended_output = self.dropout(attended_output)

        # Advanced pooling
        pooled_output = self.advanced_pooling(attended_output, attention_mask)

        # Apply second layer norm if enabled
        if self.use_layer_norm:
            pooled_output = self.layer_norm2(pooled_output)

        # Aspect-specific classification
        sentiment_logits = []
        for i in range(self.num_aspects):
            # Aspect-specific attention
            aspect_attention_weights = F.softmax(self.aspect_attention[i](pooled_output), dim=-1)
            aspect_weighted_output = pooled_output * aspect_attention_weights

            # Classification
            aspect_logits = self.aspect_classifiers[i](aspect_weighted_output)
            sentiment_logits.append(aspect_logits)

        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]

class XLMRoBERTaModel(torch.nn.Module):
    def __init__(self, pretrained_model='xlm-roberta-base', num_aspects=4, hidden_size=768, dropout=0.3):
        super(XLMRoBERTaModel, self).__init__()
        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.classifier = torch.nn.ModuleList([
            torch.nn.Sequential(
                torch.nn.Linear(hidden_size, hidden_size),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size, 4)  # 4 classes: None, Positive, Negative, Neutral
            ) for _ in range(num_aspects)
        ])
        
    def forward(self, input_ids, attention_mask):
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.last_hidden_state[:, 0]  # [CLS] token
        
        sentiment_logits = []
        for aspect_classifier in self.classifier:
            sentiment_logits.append(aspect_classifier(pooled_output))
            
        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]

class VotingEnsemble(torch.nn.Module):
    def __init__(self, num_aspects=4, models=None, weights=None):
        super(VotingEnsemble, self).__init__()
        self.models = torch.nn.ModuleList(models) if models else torch.nn.ModuleList()
        self.weights = weights if weights else [1.0] * len(self.models)
        self.num_aspects = num_aspects
        
    def forward(self, batch):
        all_logits = []
        
        # Get predictions from PhoBERT model
        if isinstance(self.models[0], PhoBERTModel):
            phobert_logits = self.models[0](
                input_ids=batch['phobert_input_ids'],
                attention_mask=batch['phobert_attention_mask'],
                token_type_ids=batch['phobert_token_type_ids']
            )
            all_logits.append(phobert_logits * self.weights[0])
        
        # Get predictions from XLM-RoBERTa model
        if isinstance(self.models[1], XLMRoBERTaModel):
            xlm_logits = self.models[1](
                input_ids=batch['xlm_input_ids'],
                attention_mask=batch['xlm_attention_mask']
            )
            all_logits.append(xlm_logits * self.weights[1])
        
        # Combine predictions using weighted average
        ensemble_logits = sum(all_logits) / sum(self.weights)
        return ensemble_logits


class AdvancedInfoXLMModel(nn.Module):
    """Advanced InfoXLM model with multi-head attention, layer normalization, and advanced pooling"""

    def __init__(self, pretrained_model='microsoft/infoxlm-base', num_aspects=4, hidden_size=768,
                 dropout=0.3, num_attention_heads=8, use_layer_norm=True, use_residual=True):
        super(AdvancedInfoXLMModel, self).__init__()

        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.hidden_size = hidden_size
        self.num_aspects = num_aspects
        self.use_layer_norm = use_layer_norm
        self.use_residual = use_residual

        # Multi-head self-attention for enhanced representation
        self.self_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_attention_heads,
            dropout=dropout,
            batch_first=True
        )

        # Layer normalization
        if self.use_layer_norm:
            self.layer_norm1 = nn.LayerNorm(hidden_size)
            self.layer_norm2 = nn.LayerNorm(hidden_size)

        # Advanced pooling strategies
        self.pooling_strategy = 'multi'  # 'cls', 'mean', 'max', 'multi'

        if self.pooling_strategy == 'multi':
            # Combine multiple pooling strategies
            pooled_size = hidden_size * 3  # CLS + Mean + Max
        else:
            pooled_size = hidden_size

        # Aspect-specific attention and classification heads
        self.aspect_attention = nn.ModuleList([
            nn.Sequential(
                nn.Linear(pooled_size, hidden_size),
                nn.Tanh(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size, 1)
            ) for _ in range(num_aspects)
        ])

        self.aspect_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(pooled_size, hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size, hidden_size // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size // 2, 4)  # 4 sentiment classes
            ) for _ in range(num_aspects)
        ])

        # Dropout layer
        self.dropout = nn.Dropout(dropout)

    def advanced_pooling(self, hidden_states, attention_mask):
        """Advanced pooling combining multiple strategies"""
        # CLS token
        cls_output = hidden_states[:, 0]  # [batch_size, hidden_size]

        if self.pooling_strategy == 'cls':
            return cls_output
        elif self.pooling_strategy == 'mean':
            # Mean pooling with attention mask
            mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()
            sum_embeddings = torch.sum(hidden_states * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            return sum_embeddings / sum_mask
        elif self.pooling_strategy == 'max':
            # Max pooling
            return torch.max(hidden_states, dim=1)[0]
        elif self.pooling_strategy == 'multi':
            # Combine CLS, mean, and max pooling
            mask_expanded = attention_mask.unsqueeze(-1).expand(hidden_states.size()).float()

            # Mean pooling
            sum_embeddings = torch.sum(hidden_states * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            mean_output = sum_embeddings / sum_mask

            # Max pooling
            max_output = torch.max(hidden_states, dim=1)[0]

            # Concatenate all pooling strategies
            return torch.cat([cls_output, mean_output, max_output], dim=-1)

    def forward(self, input_ids, attention_mask):
        # Get base encoder outputs
        encoder_outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask)
        hidden_states = encoder_outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]

        # Apply self-attention for enhanced representation
        attended_output, _ = self.self_attention(hidden_states, hidden_states, hidden_states,
                                                key_padding_mask=~attention_mask.bool())

        # Apply layer normalization and residual connection
        if self.use_layer_norm:
            attended_output = self.layer_norm1(attended_output)

        if self.use_residual:
            attended_output = attended_output + hidden_states

        # Apply dropout
        attended_output = self.dropout(attended_output)

        # Advanced pooling
        pooled_output = self.advanced_pooling(attended_output, attention_mask)

        # Apply second layer norm if enabled
        if self.use_layer_norm:
            pooled_output = self.layer_norm2(pooled_output)

        # Aspect-specific classification
        sentiment_logits = []
        for i in range(self.num_aspects):
            # Aspect-specific attention
            aspect_attention_weights = F.softmax(self.aspect_attention[i](pooled_output), dim=-1)
            aspect_weighted_output = pooled_output * aspect_attention_weights

            # Classification
            aspect_logits = self.aspect_classifiers[i](aspect_weighted_output)
            sentiment_logits.append(aspect_logits)

        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]


class AttentionBasedEnsemble(nn.Module):
    """Advanced ensemble model with attention mechanism and adaptive weighting"""

    def __init__(self, num_aspects=4, models=None, hidden_size=768, fusion_dropout=0.2):
        super(AttentionBasedEnsemble, self).__init__()

        self.models = nn.ModuleList(models) if models else nn.ModuleList()
        self.num_aspects = num_aspects
        self.hidden_size = hidden_size
        self.num_models = len(self.models)

        # Cross-attention mechanism between models
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=4,  # 4 sentiment classes
            num_heads=2,
            dropout=fusion_dropout,
            batch_first=True
        )

        # Adaptive weight learning for each aspect
        self.aspect_weight_networks = nn.ModuleList([
            nn.Sequential(
                nn.Linear(4 * self.num_models, hidden_size // 4),
                nn.ReLU(),
                nn.Dropout(fusion_dropout),
                nn.Linear(hidden_size // 4, self.num_models),
                nn.Softmax(dim=-1)
            ) for _ in range(num_aspects)
        ])

        # Feature fusion layers
        self.fusion_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(4 * self.num_models, hidden_size // 2),
                nn.ReLU(),
                nn.Dropout(fusion_dropout),
                nn.Linear(hidden_size // 2, hidden_size // 4),
                nn.ReLU(),
                nn.Dropout(fusion_dropout),
                nn.Linear(hidden_size // 4, 4)
            ) for _ in range(num_aspects)
        ])

        # Layer normalization
        self.layer_norm = nn.LayerNorm(4)

        # Temperature parameter for calibration
        self.temperature = nn.Parameter(torch.ones(num_aspects, 1))

    def forward(self, batch):
        all_logits = []

        # Get predictions from all models
        for i, model in enumerate(self.models):
            if isinstance(model, (AdvancedPhoBERTModel, PhoBERTModel)):
                model_logits = model(
                    input_ids=batch['phobert_input_ids'],
                    attention_mask=batch['phobert_attention_mask'],
                    token_type_ids=batch['phobert_token_type_ids']
                )
            elif isinstance(model, (AdvancedInfoXLMModel, XLMRoBERTaModel)):
                model_logits = model(
                    input_ids=batch['xlm_input_ids'],
                    attention_mask=batch['xlm_attention_mask']
                )
            else:
                raise ValueError(f"Unknown model type: {type(model)}")

            all_logits.append(model_logits)

        # Stack all model predictions: [batch_size, num_aspects, num_models, 4]
        stacked_logits = torch.stack(all_logits, dim=2)
        batch_size, num_aspects, num_models, num_classes = stacked_logits.shape

        # Apply cross-attention between models for each aspect
        enhanced_logits = []
        for aspect_idx in range(num_aspects):
            aspect_logits = stacked_logits[:, aspect_idx, :, :]  # [batch_size, num_models, 4]

            # Apply cross-attention
            attended_logits, _ = self.cross_attention(
                aspect_logits, aspect_logits, aspect_logits
            )

            # Apply layer normalization
            attended_logits = self.layer_norm(attended_logits)

            enhanced_logits.append(attended_logits)

        # Stack enhanced logits: [batch_size, num_aspects, num_models, 4]
        enhanced_logits = torch.stack(enhanced_logits, dim=1)

        # Adaptive weighting and fusion for each aspect
        final_logits = []
        for aspect_idx in range(num_aspects):
            aspect_enhanced = enhanced_logits[:, aspect_idx, :, :]  # [batch_size, num_models, 4]

            # Flatten for weight network input
            flattened_input = aspect_enhanced.view(batch_size, -1)  # [batch_size, num_models * 4]

            # Learn adaptive weights
            aspect_weights = self.aspect_weight_networks[aspect_idx](flattened_input)  # [batch_size, num_models]
            aspect_weights = aspect_weights.unsqueeze(-1)  # [batch_size, num_models, 1]

            # Apply adaptive weights
            weighted_logits = aspect_enhanced * aspect_weights  # [batch_size, num_models, 4]

            # Feature fusion
            fused_input = weighted_logits.view(batch_size, -1)  # [batch_size, num_models * 4]
            aspect_final_logits = self.fusion_layers[aspect_idx](fused_input)  # [batch_size, 4]

            # Apply temperature scaling for calibration
            aspect_final_logits = aspect_final_logits / self.temperature[aspect_idx]

            final_logits.append(aspect_final_logits)

        # Stack final predictions: [batch_size, num_aspects, 4]
        final_predictions = torch.stack(final_logits, dim=1)

        return final_predictions