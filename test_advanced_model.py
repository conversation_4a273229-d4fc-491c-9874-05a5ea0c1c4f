import torch
import pandas as pd
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Import our components
from ensemble_processor import create_dual_dataloaders
from process import VietnameseTextPreprocessor, preprocess_and_tokenize
from ensemble_model import AdvancedPhoBERTModel, AdvancedInfoXLMModel, AttentionBasedEnsemble
from advanced_training import AdvancedTrainer


def test_model_components():
    """Test individual model components"""
    print("Testing model components...")
    
    # Test data
    sample_texts = [
        "Khách sạn này rất tuyệt vời, nhân viên thân thiện",
        "<PERSON>ồ ăn ngon nhưng giá hơi đắt",
        "<PERSON>òng sạch sẽ, view đẹp nhưng wifi chậm"
    ]
    
    # Create sample dataframe
    df_sample = pd.DataFrame({
        'Review': sample_texts,
        'RESTAURANT#GENERAL': [1, 1, 0],
        'FOOD#QUALITY': [0, 1, 0],
        'ROOM#GENERAL': [0, 0, 1],
        'SERVICE#GENERAL': [1, 0, 0]
    })
    
    # Test preprocessing
    vn_preprocessor = VietnameseTextPreprocessor()
    df_processed = preprocess_and_tokenize(df_sample, "Review", vn_preprocessor, None, 2, 128)
    print(f"✓ Preprocessing successful. Shape: {df_processed.shape}")
    
    # Test dataloader
    train_loader, _, _ = create_dual_dataloaders(df_processed, df_processed, df_processed, 
                                               batch_size=2, max_length=128, use_infoxlm=True)
    print("✓ Dataloader creation successful")
    
    # Test model creation
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Test individual models
    phobert_model = AdvancedPhoBERTModel(num_aspects=4, hidden_size=768).to(device)
    infoxlm_model = AdvancedInfoXLMModel(num_aspects=4, hidden_size=768).to(device)
    print("✓ Individual models created successfully")
    
    # Test ensemble
    ensemble_model = AttentionBasedEnsemble(
        num_aspects=4,
        models=[phobert_model, infoxlm_model],
        hidden_size=768
    ).to(device)
    print("✓ Ensemble model created successfully")
    
    # Test forward pass
    for batch in train_loader:
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        
        with torch.no_grad():
            outputs = ensemble_model(batch)
            print(f"✓ Forward pass successful. Output shape: {outputs.shape}")
            break
    
    print("All component tests passed! ✅")
    return True


def test_training_pipeline():
    """Test the training pipeline with small data"""
    print("\nTesting training pipeline...")
    
    # Create minimal dataset for testing
    sample_data = {
        'Review': [
            "Khách sạn tuyệt vời",
            "Đồ ăn ngon",
            "Phòng sạch sẽ",
            "Nhân viên thân thiện",
            "Giá cả hợp lý"
        ] * 4,  # Repeat to have enough data
        'RESTAURANT#GENERAL': [1, 0, 0, 1, 1] * 4,
        'FOOD#QUALITY': [0, 1, 0, 0, 0] * 4,
        'ROOM#GENERAL': [0, 0, 1, 0, 0] * 4,
        'SERVICE#GENERAL': [0, 0, 0, 1, 0] * 4
    }
    
    df_train = pd.DataFrame(sample_data)
    df_val = df_train.copy()  # Use same data for validation in test
    
    # Preprocess
    vn_preprocessor = VietnameseTextPreprocessor()
    df_train = preprocess_and_tokenize(df_train, "Review", vn_preprocessor, None, 4, 128)
    df_val = preprocess_and_tokenize(df_val, "Review", vn_preprocessor, None, 4, 128)
    
    # Create dataloaders
    train_loader, val_loader, _ = create_dual_dataloaders(
        df_train, df_val, df_val, batch_size=4, max_length=128, use_infoxlm=True
    )
    
    # Create model
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    phobert_model = AdvancedPhoBERTModel(num_aspects=4, hidden_size=768).to(device)
    infoxlm_model = AdvancedInfoXLMModel(num_aspects=4, hidden_size=768).to(device)
    ensemble_model = AttentionBasedEnsemble(
        num_aspects=4,
        models=[phobert_model, infoxlm_model],
        hidden_size=768
    ).to(device)
    
    # Test trainer
    trainer = AdvancedTrainer(
        model=ensemble_model,
        device=device,
        num_aspects=4,
        gradient_accumulation_steps=1
    )
    
    trainer.setup_optimizer_and_scheduler(
        learning_rate=1e-4,  # Smaller LR for test
        num_epochs=2
    )
    
    # Run short training
    print("Running 2 epochs of training...")
    best_model_state, best_val_loss = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=2,
        save_path='test_model.pth'
    )
    
    print(f"✓ Training completed. Best validation loss: {best_val_loss:.4f}")
    print("Training pipeline test passed! ✅")
    
    return True


def analyze_model_performance(model, test_loader, device, aspect_columns):
    """Analyze model performance in detail"""
    print("\nAnalyzing model performance...")
    
    model.eval()
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            labels = batch['labels']
            
            outputs = model(batch)
            predictions = torch.argmax(outputs, dim=-1)
            true_labels = torch.argmax(labels, dim=-1)
            
            all_predictions.append(predictions.cpu().numpy())
            all_labels.append(true_labels.cpu().numpy())
    
    # Combine predictions
    all_predictions = np.vstack(all_predictions)
    all_labels = np.vstack(all_labels)
    
    # Analyze per aspect
    sentiment_names = ['None', 'Positive', 'Negative', 'Neutral']
    
    for i, aspect in enumerate(aspect_columns):
        print(f"\n--- {aspect} ---")
        aspect_pred = all_predictions[:, i]
        aspect_true = all_labels[:, i]
        
        # Classification report
        report = classification_report(aspect_true, aspect_pred, 
                                     target_names=sentiment_names, 
                                     output_dict=True, zero_division=0)
        
        print(f"Accuracy: {report['accuracy']:.3f}")
        print(f"Macro F1: {report['macro avg']['f1-score']:.3f}")
        print(f"Weighted F1: {report['weighted avg']['f1-score']:.3f}")
    
    return all_predictions, all_labels


def plot_confusion_matrices(predictions, labels, aspect_columns):
    """Plot confusion matrices for each aspect"""
    sentiment_names = ['None', 'Pos', 'Neg', 'Neu']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.ravel()
    
    for i, aspect in enumerate(aspect_columns[:4]):  # Show first 4 aspects
        cm = confusion_matrix(labels[:, i], predictions[:, i])
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=sentiment_names,
                   yticklabels=sentiment_names,
                   ax=axes[i])
        
        axes[i].set_title(f'{aspect}')
        axes[i].set_xlabel('Predicted')
        axes[i].set_ylabel('True')
    
    plt.tight_layout()
    plt.savefig('confusion_matrices.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Confusion matrices saved to confusion_matrices.png")


def hyperparameter_suggestions():
    """Suggest hyperparameter tuning based on performance"""
    print("\n" + "="*50)
    print("HYPERPARAMETER TUNING SUGGESTIONS")
    print("="*50)
    
    suggestions = [
        "1. Learning Rate Tuning:",
        "   - Try: [1e-5, 2e-5, 3e-5, 5e-5]",
        "   - Use learning rate finder if available",
        "",
        "2. Batch Size Optimization:",
        "   - Current: 16 (with gradient accumulation)",
        "   - Try: 8, 16, 32 (adjust grad accumulation accordingly)",
        "",
        "3. Loss Function Weights:",
        "   - Focal Loss weight: 0.5-0.8",
        "   - Label smoothing: 0.05-0.15",
        "   - Focal gamma: 1.5-2.5",
        "",
        "4. Model Architecture:",
        "   - Attention heads: 4, 8, 12",
        "   - Dropout: 0.1-0.4",
        "   - Fusion dropout: 0.1-0.3",
        "",
        "5. Training Strategy:",
        "   - Warmup steps: 10% of total steps",
        "   - Gradient clipping: 0.5-2.0",
        "   - Early stopping patience: 3-5 epochs",
        "",
        "6. Data Augmentation (if needed):",
        "   - Back-translation",
        "   - Synonym replacement",
        "   - Random insertion/deletion"
    ]
    
    for suggestion in suggestions:
        print(suggestion)


def main():
    """Main testing function"""
    print("Advanced ABSA Model Testing Suite")
    print("="*50)
    
    # Test components
    if test_model_components():
        print("\n✅ All components working correctly!")
    
    # Test training pipeline
    if test_training_pipeline():
        print("\n✅ Training pipeline working correctly!")
    
    # Provide hyperparameter suggestions
    hyperparameter_suggestions()
    
    print("\n" + "="*50)
    print("TESTING COMPLETED SUCCESSFULLY!")
    print("="*50)
    print("\nNext steps:")
    print("1. Run: python advanced_ensemble_main.py")
    print("2. Monitor training progress")
    print("3. Adjust hyperparameters based on results")
    print("4. Compare with baseline performance")


if __name__ == "__main__":
    main()
