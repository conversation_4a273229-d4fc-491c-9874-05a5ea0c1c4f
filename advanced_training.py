import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import numpy as np
from tqdm import tqdm


class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance"""
    
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class LabelSmoothingCrossEntropy(nn.Module):
    """Label smoothing cross entropy loss"""
    
    def __init__(self, smoothing=0.1):
        super(LabelSmoothingCrossEntropy, self).__init__()
        self.smoothing = smoothing
        
    def forward(self, inputs, targets):
        log_prob = F.log_softmax(inputs, dim=-1)
        weight = inputs.new_ones(inputs.size()) * self.smoothing / (inputs.size(-1) - 1.)
        weight.scatter_(-1, targets.unsqueeze(-1), (1. - self.smoothing))
        loss = (-weight * log_prob).sum(dim=-1).mean()
        return loss


class CombinedLoss(nn.Module):
    """Combined loss function using focal loss and label smoothing"""
    
    def __init__(self, focal_alpha=1, focal_gamma=2, smoothing=0.1, focal_weight=0.7):
        super(CombinedLoss, self).__init__()
        self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.smooth_loss = LabelSmoothingCrossEntropy(smoothing=smoothing)
        self.focal_weight = focal_weight
        
    def forward(self, inputs, targets):
        focal = self.focal_loss(inputs, targets)
        smooth = self.smooth_loss(inputs, targets)
        return self.focal_weight * focal + (1 - self.focal_weight) * smooth


class AdvancedTrainer:
    """Advanced training pipeline with modern techniques"""
    
    def __init__(self, model, device, num_aspects, 
                 focal_alpha=1, focal_gamma=2, label_smoothing=0.1,
                 gradient_accumulation_steps=1, max_grad_norm=1.0):
        
        self.model = model
        self.device = device
        self.num_aspects = num_aspects
        self.gradient_accumulation_steps = gradient_accumulation_steps
        self.max_grad_norm = max_grad_norm
        
        # Initialize combined loss
        self.criterion = CombinedLoss(
            focal_alpha=focal_alpha,
            focal_gamma=focal_gamma,
            smoothing=label_smoothing
        )
        
        # Metrics tracking
        self.train_losses = []
        self.val_losses = []
        self.train_accuracies = []
        self.val_accuracies = []
        
    def setup_optimizer_and_scheduler(self, learning_rate=2e-5, weight_decay=0.01, 
                                    scheduler_type='cosine', num_epochs=5, num_training_steps=None):
        """Setup optimizer and learning rate scheduler"""
        
        # Optimizer with different learning rates for different parts
        no_decay = ['bias', 'LayerNorm.weight']
        optimizer_grouped_parameters = [
            {
                'params': [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay)],
                'weight_decay': weight_decay,
            },
            {
                'params': [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay)],
                'weight_decay': 0.0,
            }
        ]
        
        self.optimizer = torch.optim.AdamW(optimizer_grouped_parameters, lr=learning_rate)
        
        # Learning rate scheduler
        if scheduler_type == 'cosine':
            self.scheduler = CosineAnnealingLR(self.optimizer, T_max=num_epochs)
        elif scheduler_type == 'plateau':
            self.scheduler = ReduceLROnPlateau(self.optimizer, mode='min', patience=2, factor=0.5)
        else:
            self.scheduler = None
            
    def train_epoch(self, train_loader, epoch):
        """Train for one epoch with gradient accumulation"""
        self.model.train()
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0
        
        # Reset gradients
        self.optimizer.zero_grad()
        
        progress_bar = tqdm(train_loader, desc=f'Training Epoch {epoch}')
        
        for step, batch in enumerate(progress_bar):
            # Move batch to device
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            labels = batch['labels']
            
            # Forward pass
            outputs = self.model(batch)
            
            # Calculate loss
            batch_size, num_aspects, num_classes = outputs.shape
            outputs_reshaped = outputs.view(-1, num_classes)
            labels_reshaped = labels.view(-1, num_classes)
            
            # Convert one-hot to class indices for loss calculation
            label_indices = torch.argmax(labels_reshaped, dim=1)
            
            loss = self.criterion(outputs_reshaped, label_indices)
            
            # Scale loss for gradient accumulation
            loss = loss / self.gradient_accumulation_steps
            
            # Backward pass
            loss.backward()
            
            # Gradient accumulation
            if (step + 1) % self.gradient_accumulation_steps == 0:
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
                
                # Optimizer step
                self.optimizer.step()
                self.optimizer.zero_grad()
            
            # Track metrics
            total_loss += loss.item() * self.gradient_accumulation_steps
            
            # Calculate accuracy
            predictions = torch.argmax(outputs_reshaped, dim=1)
            correct_predictions += (predictions == label_indices).sum().item()
            total_predictions += predictions.shape[0]
            
            # Update progress bar
            current_acc = correct_predictions / total_predictions
            progress_bar.set_postfix({
                'loss': f'{loss.item() * self.gradient_accumulation_steps:.4f}',
                'acc': f'{current_acc:.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.2e}'
            })
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct_predictions / total_predictions
        
        self.train_losses.append(avg_loss)
        self.train_accuracies.append(accuracy)
        
        return avg_loss, accuracy
    
    def validate_epoch(self, val_loader, epoch):
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Validation Epoch {epoch}'):
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                labels = batch['labels']
                
                outputs = self.model(batch)
                
                batch_size, num_aspects, num_classes = outputs.shape
                outputs_reshaped = outputs.view(-1, num_classes)
                labels_reshaped = labels.view(-1, num_classes)
                
                # Convert one-hot to class indices
                label_indices = torch.argmax(labels_reshaped, dim=1)
                
                loss = self.criterion(outputs_reshaped, label_indices)
                total_loss += loss.item()
                
                # Calculate accuracy
                predictions = torch.argmax(outputs_reshaped, dim=1)
                correct_predictions += (predictions == label_indices).sum().item()
                total_predictions += predictions.shape[0]
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct_predictions / total_predictions
        
        self.val_losses.append(avg_loss)
        self.val_accuracies.append(accuracy)
        
        return avg_loss, accuracy
    
    def train(self, train_loader, val_loader, num_epochs=5, save_path='best_model.pth'):
        """Complete training loop"""
        best_val_loss = float('inf')
        best_model_state = None
        
        print(f"Starting training for {num_epochs} epochs...")
        print(f"Gradient accumulation steps: {self.gradient_accumulation_steps}")
        print(f"Max gradient norm: {self.max_grad_norm}")
        
        for epoch in range(num_epochs):
            print(f'\nEpoch {epoch + 1}/{num_epochs}')
            print('-' * 50)
            
            # Training
            train_loss, train_acc = self.train_epoch(train_loader, epoch + 1)
            print(f'Training Loss: {train_loss:.4f}, Accuracy: {train_acc:.4f}')
            
            # Validation
            val_loss, val_acc = self.validate_epoch(val_loader, epoch + 1)
            print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.4f}')
            
            # Learning rate scheduling
            if self.scheduler:
                if isinstance(self.scheduler, ReduceLROnPlateau):
                    self.scheduler.step(val_loss)
                else:
                    self.scheduler.step()
                    
                print(f'Learning Rate: {self.optimizer.param_groups[0]["lr"]:.2e}')
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = self.model.state_dict().copy()
                print('✓ Best model saved!')
        
        # Save final best model
        if save_path and best_model_state:
            torch.save(best_model_state, save_path)
            print(f'\nBest model saved to {save_path}')
            print(f'Best validation loss: {best_val_loss:.4f}')
        
        return best_model_state, best_val_loss
    
    def get_training_history(self):
        """Get training history for plotting"""
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accuracies': self.train_accuracies,
            'val_accuracies': self.val_accuracies
        }
