import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import torch
import pandas as pd
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches


def plot_model_architecture():
    """Visualize the advanced ABSA model architecture"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # Define colors
    colors = {
        'input': '#E3F2FD',
        'preprocessing': '#F3E5F5', 
        'phobert': '#E1F5FE',
        'infoxlm': '#F3E5F5',
        'attention': '#FFF3E0',
        'ensemble': '#E8F5E8',
        'output': '#FFEBEE'
    }
    
    # Input layer
    input_box = FancyBboxPatch((1, 10), 2, 1, boxstyle="round,pad=0.1", 
                               facecolor=colors['input'], edgecolor='black', linewidth=2)
    ax.add_patch(input_box)
    ax.text(2, 10.5, 'Input Text\n(Vietnamese)', ha='center', va='center', fontsize=10, weight='bold')
    
    # Preprocessing
    prep_box = FancyBboxPatch((1, 8.5), 2, 1, boxstyle="round,pad=0.1",
                              facecolor=colors['preprocessing'], edgecolor='black', linewidth=2)
    ax.add_patch(prep_box)
    ax.text(2, 9, 'Text Preprocessing\n& Dual Tokenization', ha='center', va='center', fontsize=9)
    
    # PhoBERT branch
    phobert_boxes = [
        (0.5, 7, 'PhoBERT\nTokenizer'),
        (0.5, 5.5, 'Advanced\nPhoBERT Model'),
        (0.5, 4, 'Multi-Head\nAttention'),
        (0.5, 2.5, 'Advanced\nPooling'),
        (0.5, 1, 'Aspect-Specific\nHeads')
    ]
    
    for x, y, text in phobert_boxes:
        box = FancyBboxPatch((x, y), 1.5, 1, boxstyle="round,pad=0.1",
                            facecolor=colors['phobert'], edgecolor='blue', linewidth=1.5)
        ax.add_patch(box)
        ax.text(x + 0.75, y + 0.5, text, ha='center', va='center', fontsize=8)
    
    # InfoXLM branch
    infoxlm_boxes = [
        (2.5, 7, 'InfoXLM\nTokenizer'),
        (2.5, 5.5, 'Advanced\nInfoXLM Model'),
        (2.5, 4, 'Multi-Head\nAttention'),
        (2.5, 2.5, 'Advanced\nPooling'),
        (2.5, 1, 'Aspect-Specific\nHeads')
    ]
    
    for x, y, text in infoxlm_boxes:
        box = FancyBboxPatch((x, y), 1.5, 1, boxstyle="round,pad=0.1",
                            facecolor=colors['infoxlm'], edgecolor='purple', linewidth=1.5)
        ax.add_patch(box)
        ax.text(x + 0.75, y + 0.5, text, ha='center', va='center', fontsize=8)
    
    # Attention-based Ensemble
    ensemble_boxes = [
        (5, 4, 'Cross-Attention\nLayer'),
        (5, 2.5, 'Adaptive Weight\nLearning'),
        (5, 1, 'Feature Fusion\n& Classification')
    ]
    
    for x, y, text in ensemble_boxes:
        box = FancyBboxPatch((x, y), 2, 1, boxstyle="round,pad=0.1",
                            facecolor=colors['ensemble'], edgecolor='green', linewidth=2)
        ax.add_patch(box)
        ax.text(x + 1, y + 0.5, text, ha='center', va='center', fontsize=9, weight='bold')
    
    # Output
    output_box = FancyBboxPatch((5.5, -0.5), 1, 1, boxstyle="round,pad=0.1",
                               facecolor=colors['output'], edgecolor='red', linewidth=2)
    ax.add_patch(output_box)
    ax.text(6, 0, 'Sentiment\nPredictions', ha='center', va='center', fontsize=9, weight='bold')
    
    # Draw arrows
    arrows = [
        # Input to preprocessing
        ((2, 10), (2, 9.5)),
        # Preprocessing to tokenizers
        ((1.5, 8.5), (1.25, 8)),
        ((2.5, 8.5), (3.25, 8)),
        # Vertical flows in branches
        ((1.25, 7), (1.25, 6.5)),
        ((1.25, 5.5), (1.25, 5)),
        ((1.25, 4), (1.25, 3.5)),
        ((1.25, 2.5), (1.25, 2)),
        ((3.25, 7), (3.25, 6.5)),
        ((3.25, 5.5), (3.25, 5)),
        ((3.25, 4), (3.25, 3.5)),
        ((3.25, 2.5), (3.25, 2)),
        # To ensemble
        ((2, 1.5), (5, 4.5)),
        ((4, 1.5), (5, 4.5)),
        # Ensemble flow
        ((6, 4), (6, 3.5)),
        ((6, 2.5), (6, 2)),
        ((6, 1), (6, 0.5))
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    
    # Add legend
    legend_elements = [
        mpatches.Patch(color=colors['phobert'], label='PhoBERT Branch'),
        mpatches.Patch(color=colors['infoxlm'], label='InfoXLM Branch'),
        mpatches.Patch(color=colors['ensemble'], label='Attention Ensemble'),
        mpatches.Patch(color=colors['preprocessing'], label='Preprocessing')
    ]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
    
    ax.set_xlim(-0.5, 8)
    ax.set_ylim(-1, 11.5)
    ax.set_aspect('equal')
    ax.axis('off')
    ax.set_title('Advanced ABSA Model Architecture with InfoXLM', fontsize=16, weight='bold', pad=20)
    
    plt.tight_layout()
    return fig


def plot_training_pipeline():
    """Visualize the advanced training pipeline"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # Define pipeline stages
    stages = [
        (2, 9, 'Data Loading\n& Preprocessing', '#E3F2FD'),
        (2, 7.5, 'Dual Tokenization\n(PhoBERT + InfoXLM)', '#F3E5F5'),
        (2, 6, 'Model Forward Pass\n(Attention Ensemble)', '#E1F5FE'),
        (2, 4.5, 'Combined Loss\n(Focal + Label Smoothing)', '#FFF3E0'),
        (2, 3, 'Gradient Accumulation\n& Clipping', '#E8F5E8'),
        (2, 1.5, 'Optimizer Step\n(AdamW + Scheduling)', '#FFEBEE'),
        (2, 0, 'Model Evaluation\n& Checkpointing', '#F1F8E9')
    ]
    
    # Draw pipeline boxes
    for x, y, text, color in stages:
        box = FancyBboxPatch((x-0.8, y-0.4), 1.6, 0.8, boxstyle="round,pad=0.1",
                            facecolor=color, edgecolor='black', linewidth=1.5)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=10, weight='bold')
    
    # Draw arrows between stages
    for i in range(len(stages)-1):
        start_y = stages[i][1] - 0.4
        end_y = stages[i+1][1] + 0.4
        ax.annotate('', xy=(2, end_y), xytext=(2, start_y),
                   arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))
    
    # Add side annotations for techniques
    techniques = [
        (5, 8.5, 'Advanced Techniques:', 'black', 12, 'bold'),
        (5, 8, '• Multi-head Self-Attention', 'darkblue', 10, 'normal'),
        (5, 7.5, '• Advanced Pooling (CLS+Mean+Max)', 'darkblue', 10, 'normal'),
        (5, 7, '• Cross-Attention between Models', 'darkblue', 10, 'normal'),
        (5, 6.5, '• Adaptive Weight Learning', 'darkblue', 10, 'normal'),
        (5, 6, '• Temperature Scaling', 'darkblue', 10, 'normal'),
        (5, 5, 'Loss Functions:', 'darkred', 12, 'bold'),
        (5, 4.5, '• Focal Loss (α=1, γ=2)', 'darkred', 10, 'normal'),
        (5, 4, '• Label Smoothing (ε=0.1)', 'darkred', 10, 'normal'),
        (5, 3.5, '• Combined Loss (0.7 Focal + 0.3 Smooth)', 'darkred', 10, 'normal'),
        (5, 2.5, 'Optimization:', 'darkgreen', 12, 'bold'),
        (5, 2, '• AdamW with Weight Decay', 'darkgreen', 10, 'normal'),
        (5, 1.5, '• Cosine Annealing LR Schedule', 'darkgreen', 10, 'normal'),
        (5, 1, '• Gradient Clipping (max_norm=1.0)', 'darkgreen', 10, 'normal'),
        (5, 0.5, '• Gradient Accumulation', 'darkgreen', 10, 'normal')
    ]
    
    for x, y, text, color, size, weight in techniques:
        ax.text(x, y, text, ha='left', va='center', fontsize=size, 
               color=color, weight=weight)
    
    ax.set_xlim(0, 10)
    ax.set_ylim(-0.5, 10)
    ax.axis('off')
    ax.set_title('Advanced Training Pipeline with Modern ML/DL Techniques', 
                fontsize=16, weight='bold', pad=20)
    
    plt.tight_layout()
    return fig


def plot_performance_comparison():
    """Plot performance comparison between models"""
    # Sample data - replace with actual results
    models = ['Baseline\nEnsemble', 'Advanced\nEnsemble', 'Target']
    precision = [73.54, 87.5, 85.0]  # Projected improvement
    recall = [73.91, 87.8, 85.0]
    f1 = [73.72, 87.65, 85.0]
    
    x = np.arange(len(models))
    width = 0.25
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    bars1 = ax.bar(x - width, precision, width, label='Precision', color='#2196F3', alpha=0.8)
    bars2 = ax.bar(x, recall, width, label='Recall', color='#4CAF50', alpha=0.8)
    bars3 = ax.bar(x + width, f1, width, label='F1-Score', color='#FF9800', alpha=0.8)
    
    # Add value labels on bars
    for bars in [bars1, bars2, bars3]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    ax.set_xlabel('Models', fontsize=12, fontweight='bold')
    ax.set_ylabel('Performance (%)', fontsize=12, fontweight='bold')
    ax.set_title('Performance Comparison: Baseline vs Advanced Model', 
                fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(models)
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 100)
    
    # Add improvement annotations
    improvement_f1 = f1[1] - f1[0]
    ax.annotate(f'+{improvement_f1:.1f}% improvement', 
               xy=(1, f1[1]), xytext=(1.5, f1[1] + 5),
               arrowprops=dict(arrowstyle='->', color='red', lw=2),
               fontsize=12, fontweight='bold', color='red')
    
    plt.tight_layout()
    return fig


def plot_training_history(history):
    """Plot training history"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    epochs = range(1, len(history['train_losses']) + 1)
    
    # Loss plots
    ax1.plot(epochs, history['train_losses'], 'b-', label='Training Loss', linewidth=2)
    ax1.plot(epochs, history['val_losses'], 'r-', label='Validation Loss', linewidth=2)
    ax1.set_title('Training and Validation Loss', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Accuracy plots
    ax2.plot(epochs, [acc * 100 for acc in history['train_accuracies']], 'b-', 
            label='Training Accuracy', linewidth=2)
    ax2.plot(epochs, [acc * 100 for acc in history['val_accuracies']], 'r-', 
            label='Validation Accuracy', linewidth=2)
    ax2.set_title('Training and Validation Accuracy', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Loss difference
    loss_diff = [abs(t - v) for t, v in zip(history['train_losses'], history['val_losses'])]
    ax3.plot(epochs, loss_diff, 'g-', linewidth=2)
    ax3.set_title('Training-Validation Loss Gap', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Loss Difference')
    ax3.grid(True, alpha=0.3)
    
    # Learning curve
    ax4.plot(epochs, history['train_losses'], 'b-', alpha=0.7, label='Train')
    ax4.plot(epochs, history['val_losses'], 'r-', alpha=0.7, label='Validation')
    ax4.fill_between(epochs, history['train_losses'], history['val_losses'], 
                    alpha=0.2, color='gray')
    ax4.set_title('Learning Curve Overview', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Loss')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig


def save_all_visualizations():
    """Save all visualization plots"""
    # Model architecture
    fig1 = plot_model_architecture()
    fig1.savefig('model_architecture.png', dpi=300, bbox_inches='tight')
    plt.close(fig1)
    
    # Training pipeline
    fig2 = plot_training_pipeline()
    fig2.savefig('training_pipeline.png', dpi=300, bbox_inches='tight')
    plt.close(fig2)
    
    # Performance comparison
    fig3 = plot_performance_comparison()
    fig3.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close(fig3)
    
    print("All visualizations saved successfully!")
    print("Files created:")
    print("- model_architecture.png")
    print("- training_pipeline.png") 
    print("- performance_comparison.png")


if __name__ == "__main__":
    save_all_visualizations()
