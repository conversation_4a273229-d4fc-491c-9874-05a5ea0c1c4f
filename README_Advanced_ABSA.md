# Advanced ABSA Ensemble with InfoXLM

Hệ thống phân tích cảm xúc dựa trên khía c<PERSON>nh (ABSA) tiên tiến sử dụng InfoXLM và các kỹ thuật ML/DL hiện đại để cải thiện hiệu suất từ 73% lên >85%.

## 🚀 Tính năng chính

### 1. **Advanced Model Architecture**
- **InfoXLM Integration**: Sử dụng Microsoft InfoXLM thay vì XLM-RoBERTa
- **Multi-Head Self-Attention**: Tăng cường khả năng hiểu ngữ cảnh
- **Advanced Pooling**: <PERSON><PERSON><PERSON> hợ<PERSON> CLS, Mean, và Max pooling
- **Residual Connections**: Cải thiện gradient flow
- **Layer Normalization**: Ổn định quá trình training

### 2. **Attention-Based Ensemble**
- **Cross-Attention**: Tương tác gi<PERSON>a các model
- **Adaptive Weighting**: <PERSON><PERSON><PERSON> trọng số tự động cho từng aspect
- **Feature Fusion**: <PERSON><PERSON><PERSON> hợ<PERSON> thông tin từ nhiều nguồn
- **Temperature Scaling**: Calibration cho predictions

### 3. **Advanced Training Pipeline**
- **Focal Loss**: Xử lý class imbalance
- **Label Smoothing**: Giảm overfitting
- **Gradient Accumulation**: Training với batch size lớn
- **Learning Rate Scheduling**: Cosine annealing
- **Gradient Clipping**: Ổn định training

## 📁 Cấu trúc Files

```
├── ensemble_model.py              # Advanced models (PhoBERT, InfoXLM, Ensemble)
├── advanced_training.py           # Training pipeline với modern techniques
├── advanced_ensemble_main.py      # Script chính để chạy training
├── ensemble_processor.py          # Data processing với dual tokenization
├── model_visualization.py         # Visualization tools
├── test_advanced_model.py         # Testing và validation
├── performance_analysis.py        # Phân tích hiệu suất chi tiết
└── README_Advanced_ABSA.md        # Hướng dẫn này
```

## 🛠️ Cài đặt

```bash
# Cài đặt dependencies
pip install torch transformers pandas numpy matplotlib seaborn scikit-learn tqdm

# Đảm bảo có GPU (khuyến nghị)
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

## 🚀 Cách sử dụng

### 1. **Test Components (Khuyến nghị chạy trước)**
```bash
python test_advanced_model.py
```

### 2. **Training Model**
```bash
python advanced_ensemble_main.py
```

### 3. **Phân tích kết quả**
```bash
python performance_analysis.py
```

### 4. **Tạo visualizations**
```bash
python model_visualization.py
```

## ⚙️ Configuration

Chỉnh sửa hyperparameters trong `advanced_ensemble_main.py`:

```python
class Config:
    # Model settings
    HIDDEN_SIZE = 768
    DROPOUT = 0.3
    NUM_ATTENTION_HEADS = 8
    
    # Training settings
    LEARNING_RATE = 2e-5
    BATCH_SIZE = 16
    NUM_EPOCHS = 5
    GRADIENT_ACCUMULATION_STEPS = 2
    
    # Loss function
    FOCAL_ALPHA = 1.0
    FOCAL_GAMMA = 2.0
    LABEL_SMOOTHING = 0.1
```

## 📊 Kết quả mong đợi

| Metric | Baseline | Advanced Model | Improvement |
|--------|----------|----------------|-------------|
| Precision | 73.54% | **>85%** | **+11.46%** |
| Recall | 73.91% | **>85%** | **+11.09%** |
| F1-Score | 73.72% | **>85%** | **+11.28%** |

## 🔧 Hyperparameter Tuning

### Learning Rate
```python
# Thử các giá trị
LEARNING_RATES = [1e-5, 2e-5, 3e-5, 5e-5]
```

### Loss Function Weights
```python
# Focal Loss vs Label Smoothing
FOCAL_WEIGHT = 0.7  # 0.5-0.8
LABEL_SMOOTHING = 0.1  # 0.05-0.15
```

### Model Architecture
```python
# Attention heads
NUM_ATTENTION_HEADS = 8  # 4, 8, 12

# Dropout rates
DROPOUT = 0.3  # 0.1-0.4
FUSION_DROPOUT = 0.2  # 0.1-0.3
```

## 📈 Monitoring Training

### 1. **Training Logs**
- Loss và accuracy theo epoch
- Learning rate scheduling
- Gradient norms

### 2. **Visualizations**
- Training curves
- Model architecture diagram
- Performance comparison

### 3. **Evaluation Metrics**
- Per-aspect performance
- Confusion matrices
- Detailed classification reports

## 🎯 Kỹ thuật cải thiện

### 1. **Model Level**
- Multi-head attention cho context understanding
- Advanced pooling strategies
- Cross-attention between models
- Temperature scaling cho calibration

### 2. **Training Level**
- Combined loss (Focal + Label Smoothing)
- Gradient accumulation
- Learning rate scheduling
- Early stopping

### 3. **Ensemble Level**
- Adaptive weight learning
- Feature fusion networks
- Cross-model attention

## 🔍 Troubleshooting

### Memory Issues
```python
# Giảm batch size
BATCH_SIZE = 8
GRADIENT_ACCUMULATION_STEPS = 4
```

### Slow Training
```python
# Sử dụng mixed precision
# Thêm vào trainer
use_amp = True
```

### Poor Performance
1. Kiểm tra data quality
2. Adjust learning rate
3. Increase model complexity
4. Try different loss weights

## 📋 Checklist

- [ ] Test components với `test_advanced_model.py`
- [ ] Chạy training với `advanced_ensemble_main.py`
- [ ] Kiểm tra kết quả trong `predictions/`
- [ ] Phân tích performance với `performance_analysis.py`
- [ ] Tạo visualizations với `model_visualization.py`
- [ ] So sánh với baseline (73.72% F1)
- [ ] Fine-tune hyperparameters nếu cần

## 🎉 Expected Improvements

Với architecture và techniques này, bạn có thể mong đợi:

1. **+10-15% improvement** trong F1-score
2. **Better aspect-specific performance**
3. **More stable training**
4. **Better generalization**

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra logs trong quá trình training
2. Chạy `test_advanced_model.py` để debug
3. Xem visualization để hiểu model behavior
4. Adjust hyperparameters dựa trên performance analysis

---

**Good luck với việc cải thiện ABSA model! 🚀**
