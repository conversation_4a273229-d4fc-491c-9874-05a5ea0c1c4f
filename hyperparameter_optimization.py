import optuna
import torch
import numpy as np
from sklearn.metrics import f1_score
from enhanced_model import AdvancedPhoBERTModel, AdvancedInfoXLMModel, AttentionBasedEnsemble
from enhanced_processor import AdvancedTrainer, EarlyStopping
import warnings
warnings.filterwarnings('ignore')

class HyperparameterOptimizer:
    """Hyperparameter optimization using Optuna"""
    
    def __init__(self, train_loader, val_loader, device, num_aspects):
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        self.num_aspects = num_aspects
        
    def objective(self, trial):
        """Objective function for Optuna optimization"""
        
        # Suggest hyperparameters
        base_lr = trial.suggest_float('base_lr', 1e-6, 1e-4, log=True)
        layer_decay = trial.suggest_float('layer_decay', 0.8, 0.95)
        dropout = trial.suggest_float('dropout', 0.1, 0.5)
        fusion_dropout = trial.suggest_float('fusion_dropout', 0.05, 0.3)
        label_smoothing = trial.suggest_float('label_smoothing', 0.05, 0.2)
        mixup_alpha = trial.suggest_float('mixup_alpha', 0.1, 0.4)
        gradient_clip = trial.suggest_float('gradient_clip', 0.5, 2.0)
        use_layer_norm = trial.suggest_categorical('use_layer_norm', [True, False])
        use_residual = trial.suggest_categorical('use_residual', [True, False])
        
        try:
            # Create models with suggested hyperparameters
            phobert_model = AdvancedPhoBERTModel(
                pretrained_model='vinai/phobert-base',
                num_aspects=self.num_aspects,
                hidden_size=768,
                dropout=dropout,
                use_layer_norm=use_layer_norm,
                use_residual=use_residual
            ).to(self.device)
            
            infoxlm_model = AdvancedInfoXLMModel(
                pretrained_model='microsoft/infoxlm-base',
                num_aspects=self.num_aspects,
                hidden_size=768,
                dropout=dropout,
                use_layer_norm=use_layer_norm,
                use_residual=use_residual
            ).to(self.device)
            
            # Create ensemble
            ensemble_model = AttentionBasedEnsemble(
                num_aspects=self.num_aspects,
                models=[phobert_model, infoxlm_model],
                hidden_size=768,
                fusion_dropout=fusion_dropout
            ).to(self.device)
            
            # Create trainer
            trainer = AdvancedTrainer(
                model=ensemble_model,
                device=self.device,
                use_mixup=True,
                mixup_alpha=mixup_alpha,
                label_smoothing=label_smoothing,
                gradient_clip_val=gradient_clip
            )
            
            # Create optimizer
            optimizer = trainer.get_layerwise_optimizer(base_lr=base_lr, layer_decay=layer_decay)
            
            # Create scheduler
            total_steps = len(self.train_loader) * 3  # 3 epochs for quick evaluation
            scheduler = trainer.get_cosine_scheduler(optimizer, total_steps, warmup_steps=total_steps // 10)
            
            # Early stopping
            early_stopping = EarlyStopping(patience=2, min_delta=0.001, restore_best_weights=True)
            
            # Quick training for 3 epochs
            best_val_f1 = 0
            for epoch in range(3):
                # Training
                ensemble_model.train()
                for batch in self.train_loader:
                    loss, accuracy = trainer.train_step(batch, optimizer, scheduler)
                
                # Validation
                ensemble_model.eval()
                all_predictions = []
                all_labels = []
                
                with torch.no_grad():
                    for batch in self.val_loader:
                        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                                for k, v in batch.items()}
                        labels = batch['labels']
                        
                        outputs = ensemble_model(batch)
                        predictions = torch.argmax(outputs, dim=-1)
                        true_labels = torch.argmax(labels, dim=-1)
                        
                        all_predictions.extend(predictions.cpu().numpy().flatten())
                        all_labels.extend(true_labels.cpu().numpy().flatten())
                
                # Calculate F1 score
                val_f1 = f1_score(all_labels, all_predictions, average='weighted')
                
                if val_f1 > best_val_f1:
                    best_val_f1 = val_f1
                
                # Report intermediate value for pruning
                trial.report(val_f1, epoch)
                
                # Handle pruning
                if trial.should_prune():
                    raise optuna.exceptions.TrialPruned()
            
            return best_val_f1
            
        except Exception as e:
            print(f"Trial failed with error: {e}")
            return 0.0
    
    def optimize(self, n_trials=50, timeout=3600):
        """Run hyperparameter optimization"""
        
        # Create study
        study = optuna.create_study(
            direction='maximize',
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=1)
        )
        
        # Optimize
        study.optimize(self.objective, n_trials=n_trials, timeout=timeout)
        
        # Print results
        print("Hyperparameter Optimization Results:")
        print("=" * 50)
        print(f"Best F1 Score: {study.best_value:.4f}")
        print("Best Parameters:")
        for key, value in study.best_params.items():
            print(f"  {key}: {value}")
        
        return study.best_params, study.best_value

class AdvancedEnsembleStrategies:
    """Advanced ensemble strategies beyond basic voting"""
    
    @staticmethod
    def create_stacking_ensemble(base_models, meta_model, train_loader, val_loader, device):
        """Create stacking ensemble with meta-learner"""
        
        # Generate base model predictions on validation set
        base_predictions = []
        
        for model in base_models:
            model.eval()
            model_preds = []
            
            with torch.no_grad():
                for batch in val_loader:
                    batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                            for k, v in batch.items()}
                    outputs = model(batch)
                    model_preds.append(outputs.cpu())
            
            base_predictions.append(torch.cat(model_preds, dim=0))
        
        # Stack predictions as input for meta-learner
        stacked_predictions = torch.stack(base_predictions, dim=0)
        
        return stacked_predictions
    
    @staticmethod
    def dynamic_ensemble_weighting(models, val_loader, device, num_aspects):
        """Learn dynamic ensemble weights based on aspect-specific performance"""
        
        aspect_weights = torch.ones(len(models), num_aspects) / len(models)
        
        # Calculate aspect-specific performance for each model
        for model_idx, model in enumerate(models):
            model.eval()
            aspect_correct = torch.zeros(num_aspects)
            aspect_total = torch.zeros(num_aspects)
            
            with torch.no_grad():
                for batch in val_loader:
                    batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                            for k, v in batch.items()}
                    labels = batch['labels']
                    
                    outputs = model(batch)
                    predictions = torch.argmax(outputs, dim=-1)
                    true_labels = torch.argmax(labels, dim=-1)
                    
                    for aspect_idx in range(num_aspects):
                        aspect_preds = predictions[:, aspect_idx]
                        aspect_true = true_labels[:, aspect_idx]
                        
                        aspect_correct[aspect_idx] += (aspect_preds == aspect_true).sum().item()
                        aspect_total[aspect_idx] += aspect_preds.size(0)
            
            # Calculate accuracy for each aspect
            aspect_accuracy = aspect_correct / (aspect_total + 1e-8)
            aspect_weights[model_idx] = aspect_accuracy
        
        # Normalize weights
        aspect_weights = torch.softmax(aspect_weights, dim=0)
        
        return aspect_weights
    
    @staticmethod
    def uncertainty_based_ensemble(models, batch, device, temperature=1.0):
        """Ensemble based on prediction uncertainty"""
        
        all_logits = []
        all_uncertainties = []
        
        for model in models:
            model.eval()
            with torch.no_grad():
                logits = model(batch)
                
                # Calculate uncertainty using entropy
                probs = torch.softmax(logits / temperature, dim=-1)
                entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
                uncertainty = entropy.mean(dim=1)  # Average across aspects
                
                all_logits.append(logits)
                all_uncertainties.append(uncertainty)
        
        # Weight by inverse uncertainty (more certain predictions get higher weight)
        uncertainties = torch.stack(all_uncertainties, dim=0)
        weights = torch.softmax(-uncertainties, dim=0)
        
        # Weighted combination
        weighted_logits = sum(w.unsqueeze(-1).unsqueeze(-1) * logits 
                             for w, logits in zip(weights, all_logits))
        
        return weighted_logits

def run_hyperparameter_optimization(train_loader, val_loader, device, num_aspects, 
                                   n_trials=20, timeout=1800):
    """Run complete hyperparameter optimization"""
    
    print("Starting Hyperparameter Optimization...")
    print("=" * 50)
    
    optimizer = HyperparameterOptimizer(train_loader, val_loader, device, num_aspects)
    best_params, best_score = optimizer.optimize(n_trials=n_trials, timeout=timeout)
    
    print(f"\nOptimization completed!")
    print(f"Best F1 Score achieved: {best_score:.4f}")
    
    return best_params, best_score

def create_optimized_ensemble(best_params, num_aspects, device):
    """Create ensemble with optimized hyperparameters"""
    
    # Create models with best parameters
    phobert_model = AdvancedPhoBERTModel(
        pretrained_model='vinai/phobert-base',
        num_aspects=num_aspects,
        hidden_size=768,
        dropout=best_params['dropout'],
        use_layer_norm=best_params['use_layer_norm'],
        use_residual=best_params['use_residual']
    ).to(device)
    
    infoxlm_model = AdvancedInfoXLMModel(
        pretrained_model='microsoft/infoxlm-base',
        num_aspects=num_aspects,
        hidden_size=768,
        dropout=best_params['dropout'],
        use_layer_norm=best_params['use_layer_norm'],
        use_residual=best_params['use_residual']
    ).to(device)
    
    # Create optimized ensemble
    ensemble_model = AttentionBasedEnsemble(
        num_aspects=num_aspects,
        models=[phobert_model, infoxlm_model],
        hidden_size=768,
        fusion_dropout=best_params['fusion_dropout']
    ).to(device)
    
    return ensemble_model, best_params
